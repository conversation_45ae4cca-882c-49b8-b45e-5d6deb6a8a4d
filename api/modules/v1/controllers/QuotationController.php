<?php

namespace api\modules\v1\controllers;

use api\modules\v1\models\QuotationListing;
use common\helpers\FileManager;
use common\models\enum\Key;
use common\models\Quotation;
use common\models\User;
use Yii;
use yii\base\Exception;
use yii\helpers\ArrayHelper;

class QuotationController extends BaseApiController
{
    public function actionList()
    {
        /** @var User $user */
        $user = $this->_checkAuth();
        $params = Yii::$app->request->get();

        $quotationListing = new QuotationListing();
        $quotationListing->setAttributes($params);
        $quotationListing->currentUser = $user;
        $quotationListing->businessId = $this->business->id;

        if (!$quotationListing->validate()) {
            $this->_sendErrorResponse(200, $quotationListing->getErrorSummary(true)[0], 101);
        }

        $response = [
            'quotationList' => $quotationListing->search(),
            'pagination' => $quotationListing->pagination(),
        ];

        if (isset($params['debug'])) {
            $response['sql'] = $quotationListing->sql;
        }

        $this->_sendResponse($response, 'Quotation List data sent successfully!');
    }


    public function actionSync($lastTimestamp = 0)
    {
        $salesUser = $this->_checkAuth();

        $findQuery = Quotation::find();
        $findQuery->andWhere('createdAt > FROM_UNIXTIME(:lastTimestamp)', [':lastTimestamp' => $lastTimestamp])
            ->orWhere('updatedAt > FROM_UNIXTIME(:lastTimestamp)', [':lastTimestamp' => $lastTimestamp])
            ->andWhere(['isDeleted' => 0]);
        $findQuery->andWhere(['businessId' => $this->business->id]);
        if ($this->identity->isUser() && !$salesUser->isAdmin) {
            $subUserIds = $salesUser->getSubIds();
            $findQuery->andWhere(['in', 'assignedToId', $subUserIds]);
        }
        $arrList = $findQuery->all();
        $templateList = [];
        if ($this->identity->isUser() && !$salesUser->isAdmin) {
            $findQuery->andWhere('createdAt > FROM_UNIXTIME(:lastTimestamp)', [':lastTimestamp' => $lastTimestamp])
                ->orWhere('updatedAt > FROM_UNIXTIME(:lastTimestamp)', [':lastTimestamp' => $lastTimestamp])
                ->andWhere(['isTemplate'=>1])
                ->andWhere(['isDeleted' => 0]);
            $findQuery->andWhere(['businessId' => $this->business->id]);
            $templateList = $findQuery->all();
        }
        $rAdd['quotationList'] = array_merge($arrList, $templateList);
        $response['add'] = $rAdd;
        if ($lastTimestamp != 0) {
            //  Quotation
            $quotationList = Quotation::find()->select(['id'])
                ->where('deletedAt > FROM_UNIXTIME(:lastTimestamp)', [':lastTimestamp' => $lastTimestamp])
                ->andWhere(['isDeleted' => 1])->andWhere(['businessId' => $this->business->id])
                ->all();

            $rRemove['quotationIds'] = ArrayHelper::getColumn($quotationList, 'id');
            $response['remove'] = $rRemove; // Removed data list
        }

        $message = "Quotation Sync list sent successfully";
        return $this->_sendResponse($response, $message);
    }

    public function actionUpdate()
    {
        $this->actionCreate(true);
    }

    public function actionRemove()
    {
        /** @var User $user */
        $user = $this->_checkAuth();

        $request = Yii::$app->request;
        $quotationId = $request->post('id');
        /** @var User $user */
        $quotation = Quotation::findByPk($quotationId);
        if (!$user->isAdmin && $quotation->assignedToId != $user->id) {
            return $this->_sendErrorResponse(200, 'only owner has rights to perform this action', 101);
        }
        if ($quotation == null) {
            return $this->_sendErrorResponse(200, 'Invalid quotation id', 101);

        }

        if ($quotation->remove()) {
            $response = ['quotationId' => $quotation->id];
            $message = 'Quotation removed successfully!';
            return $this->_sendResponse($response, $message);
        }
        return $this->_sendErrorResponse(200, "Server side issues!", 105); // user doesn't exits!
    }


    public function actionCreate($isUpdate = false)
    {
        /** @var User $salesUser */
        $salesUser = $this->_checkAuth();
        $request = Yii::$app->request;
        $isPurchaseOrder = toBool($request->post('isPurchaseOrder', 0));
        $isBudget = toBool($request->post('isBudget',0));
        $isQuotation = 0;
        if (!$isPurchaseOrder && !$isBudget){
            $isQuotation = 1;
        }
        $isSkipSubscriptionVerification = toBool($request->post(Key::isSkipSubscriptionVerification, false));

        if (!$isSkipSubscriptionVerification && (($isPurchaseOrder && !$salesUser->business->canGeneratePurchaseOrder()) ||
                ($isBudget && !$salesUser->business->canGenerateBudget()) ||
                ($isQuotation && !$salesUser->business->canGenerateQuotation())
            )) {
            return $this->_sendErrorResponse(200, "Your free usage limit is over! Please subscribe to premium plan!", 402);
            // TODO : Your free usage limit is over! Please Update your app to latest version OR subscribe to premium plan!
        }

        try {
            $newQuotation = Quotation::CreateOrUpdate($request, $salesUser);
        } catch (Exception|\Exception $e) {
            return $this->_sendErrorResponse(200, $e->getMessage(), $e->getCode());
        }
        $newQuotation->refresh();
        $salesUser->refresh();
        $businessStats = $salesUser->businessStats;
        $response = ['quotation' => $newQuotation, 'pdfFileBase64' => $newQuotation->pdfFileBase64,
            'totalUsageCount' => $businessStats->totalUsageCount,
            'totalFreeUsageCount' => $businessStats->totalFreeUsageCount,
            'freeUsageCount' => $businessStats->quotationFreeUsageLimit,
            'quotationCount' => $businessStats->quotationCount,
            'quotationFreeUsageLimit' => $businessStats->quotationFreeUsageLimit,
            'poCount' => $businessStats->poCount,
            'poFreeUsageLimit' => $businessStats->poFreeUsageLimit,
            'budgetCount' => $businessStats->budgetCount,
            'budgetFreeUsageLimit' => $businessStats->budgetFreeUsageLimit,
        ];


        $message = $isUpdate ? "Quotation updated successfully!" : "New quotation created successfully!";

        return $this->_sendResponse($response, $message);
    }

    public function actionGeneratePdf()
    {
        /** @var User $salesUser */
        $salesUser = $this->_checkAuth();
        $request = Yii::$app->request;
        $debug = $request->get('debug', false);
        $quoteId = $request->get('id');
        $newQuotation = Quotation::findByPk($quoteId);
        if ($newQuotation == null) {
            return $this->_sendErrorResponse(200, 'Invalid quotation id', 101);
        }

        if ($newQuotation->pdfFileUrl && $newQuotation->getPdfFilePath()!= null) {
            $pdfFile = file_get_contents($newQuotation->getPdfFilePath());
            $newQuotation->pdfFileBase64 = base64_encode($pdfFile);
            $response = ['quotation' => $newQuotation, 'pdfFileBase64' => $newQuotation->pdfFileBase64];
            $message = "Quotation generated successfully!";
            return $this->_sendResponse($response, $message);
        }

        $newQuotation->generatePDF( false);

        $response = [];
        if ($debug) {
            $relPath = sprintf(QUOTATION_DIR, $newQuotation->businessId, date('Y'), date('m'));

            // Save PDF file
            $pdfFileUrl = FileManager::saveFileContent($newQuotation->pdfOutputData, $relPath, $newQuotation->pdfFileName);

            // Save HTML file
            $htmlFileName = basename($newQuotation->pdfFileName, ".pdf") . ".html";
            $htmlFileUrl = FileManager::saveFileContent($newQuotation->pdfHtmlContent, $relPath, $htmlFileName);

            $response = ['pdfFileUrl' => $pdfFileUrl, 'htmlFileUrl' => $htmlFileUrl];
        }

        $response = array_merge(
            $response,
            ['quotation' => $newQuotation, 'pdfFileBase64' => $newQuotation->pdfFileBase64]
        );
        $message = "Quotation generated successfully!";
        return $this->_sendResponse($response, $message);
    }

    public function actionSendPdfMail()
    {
        /** @var User $salesUser */
        $salesUser = $this->_checkAuth();
        $request = Yii::$app->request;

        $quoteId = $request->get('id');
        $newQuotation = Quotation::findByPk($quoteId);
        if ($newQuotation == null) {
            return $this->_sendErrorResponse(200, 'Invalid quotation id', 101);
        }

        if (!$newQuotation->sendMailToCustomer()) {
            return $this->_sendErrorResponse(200, 'There is server side issue in sending email! Please try again later!', 105);
        }

        $response = ['quotation' => $newQuotation];
        $message = "Quotation mail has been sent successfully!";
        return $this->_sendResponse($response, $message);
    }


    public function actionDetail($id)
    {
        $salesUser = $this->_checkAuth();
        $findQuery = Quotation::find();
        $findQuery->alias('q');
        $findQuery->select(['q.*', 'c.name as customer_name',
            'u.name as salesUser_name',
        ]);

        $findQuery->andWhere(['q.id' => $id]);
        $findQuery->joinWith('customer c');
        $findQuery->joinWith('assignedTo u');
        $findQuery->joinWith('quotationItems');
//        $findQuery->with('quotationItems.product');
//        $findQuery->with('quotationItems.product.category');
        if (IS_PREMIUM_APP) {
            $findQuery->joinWith('quotationLogs');
        }

        $sql = $findQuery->createCommand()->rawSql;
        $quotation = $findQuery->asArray()->one();

        if ($quotation == null) {
            return $this->_sendErrorResponse(200, 'Invalid quotation id', 101);
        }
        $response = ['quotation' => $quotation];
        $message = "Quotation detail sent successfully";
        return $this->_sendResponse($response, $message);
    }


    public function actionUpdateStatus($userId = null)
    {
        if ($userId) {
            /** @var User $salesUser */
            $salesUser = User::findByPk($userId);
        } else {
            /** @var User $salesUser */
            $salesUser = $this->_checkAuth();
        }

        $request = Yii::$app->request;

        $quoteId = $request->post('id');
        $status = $request->post('status');
        $statusText = $request->post('statusText');

        if ($quoteId == null || $status == null) {
            return $this->_sendErrorResponse(200, 'Missing input params!', 101);
        }

        $quotation = Quotation::findByPk($quoteId);
        if ($quotation == null) {
            return $this->_sendErrorResponse(200, 'Invalid quotation id', 101);
        }

        $quotation->setAttributes($_POST);  // load all attributes (in new User model)

        $quotation->statusUpdatedById = $salesUser->id;
//        $quotation->status = Quotation::STATUS_NEW;

        if (!$quotation->validate()) {
            $errors = $quotation->getErrorSummary(true)[0];
            return $this->_sendErrorResponse(200, $errors, 101);
        }
        if (!$quotation->save()) {
            return $this->_sendErrorResponse(200, $quotation->getErrorSummary(true)[0], 101);
        }

        $response = ['quotation' => $quotation];
        $message = "Quotation status updated successfully!";
        return $this->_sendResponse($response, $message);
    }


}