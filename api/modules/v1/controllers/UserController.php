<?php

namespace api\modules\v1\controllers;

use api\modules\v1\models\UserListing;
use common\helpers\FileManager;
use common\models\enum\Key;
use common\models\enum\SocialKeys;
use common\models\NotificationMessage;
use common\models\User;
use common\models\UserBusiness;
use common\models\UserDevice;
use common\models\UserSocialProvider;
use common\services\EmailClient;
use stdClass;
use Throwable;
use Yii;
use yii\db\StaleObjectException;
use yii\web\UploadedFile;

class UserController extends BaseApiController
{
    public function actionSignup()
    {
        $request = Yii::$app->request;
        if ($request->post()) {

            $email = strtolower($request->post('email'));
            $password = $request->post('password');
            /** @var User $user */
            $user = User::findByEmail($email);
            if ($user != null) {
                return $this->_sendErrorResponse(200, "Your email is already in use! Kindly Login with your email!", 101); // email is already exists
            }

            $isStrengthOk = User::checkPasswordStrength($password);
            if (!$isStrengthOk) {
                return $this->_sendErrorResponse(200, "please input minimum 6 character password", 101); // email is already exists
            }

            $user = new User();

            $user->setAttributes($_POST);  // load all attributes (in new User model)
            if (!empty($_POST['phoneNumber'])) {
                $user->setScenario(User::SCENARIO_VALIDATE_PHONE);
            }
            $user->email = $email;
            $user->profileImage = UploadedFile::getInstanceByName('profileImage');
            $user->signatureImgFile = UploadedFile::getInstanceByName('signatureImg');
            $user->setPassword($password);

            $user->generateAccessToken();
            $user->generateOTPCode();
            if (!$user->validate()) {
                return $this->_sendErrorResponse(200, $user->getErrorSummary(true)[0], 101);
            }
            $user->isAdmin = 1;
            if (IS_PREMIUM_APP){
                $user->status = User::STATUS_BLOCKED;
            }
            $user->save();
            $user->refresh();
            if ($user->status === User::STATUS_BLOCKED) {
                $this->sendSignupNotificationEmail($user);
                return $this->_sendErrorResponse(200, "Your details have been sent to admin for approval. We will update you soon once your details have been verified and approved by Admin!", 400);
            }
            $token = $user->getAccessToken();
            $response = ['token' => $token, 'user' => $this->_userData($user)];
            $message = 'User added successfully!';
            return $this->_sendResponse($response, $message);
        }
    }

    /**
     * Sends payment notification email to admin
     */
    public function sendSignupNotificationEmail($user)
    {
        // Set the timezone to IST (Indian Standard Time)
        date_default_timezone_set('Asia/Kolkata');

        $todayDate = date("d.m.Y");
        $composeMail = EmailClient::getInstance()->getMailer()
            ->compose(
                ['html' => 'user-signup'],
                ['user' => $user],
            )
            ->setFrom([Yii::$app->params['noReplyEmail'] => Yii::$app->params['noReplyName']])
            ->setTo(Yii::$app->params['adminEmail'])
            ->setSubject('New User Signup - ' . Yii::$app->name . " - $todayDate");
        $isSent = $composeMail->send();
        if ($isSent !== true) {
            Yii::error("Error occurred in sending subscription email");
        }
    }


    public function actionSocialLogin()
    {
        $request = Yii::$app->request;
        $providerId = $request->post('providerId');
        $isNewRecord = false;
        /** @var UserSocialProvider $socialProvider */
        $socialProvider = UserSocialProvider::findOne(['providerId' => $providerId]);
        if ($socialProvider == null) {
            $socialProvider = new UserSocialProvider();
            $user = null;
        } else {
            $user = $socialProvider->user;
        }

        $socialProvider->setAttributes($request->post());
        if (!empty($socialProvider->name)) {
            $socialProvider->displayName = $socialProvider->name;
        }

        /** @var User $user */
        if ($user == null) {    // if user doesn't exists then create new user record.
            $socialProvider->setScenario(UserSocialProvider::SCENARIO_CREATE);
            $firstName = $request->post('firstName');
            if ($socialProvider->provider == SocialKeys::APPLE && empty($socialProvider->email)) {
                $socialProvider->email = "apple-" . $socialProvider->providerId . "@apple.id";
            }
            if (empty($socialProvider->name) && !empty($firstName)) {
                $socialProvider->name = $firstName;
            }
            $user = User::findByEmail($socialProvider->email);
            if ($user == null) {    // if no user available for given email then create new user.
                $user = new User();
                $user->email = $socialProvider->email;
                $user->name = $socialProvider->name;
                $user->setPassword($socialProvider->providerToken);
                $user->status = User::STATUS_ACTIVE;
                $user->generateAccessToken();
                $user->isAdmin = 1;
                $user->save();
                if ($user->id == null) {
                    return $this->_sendErrorResponse(200, $user->getErrorSummary(true)[0], 101);
                }
                $isNewRecord = true;
            }
            $socialProvider->userId = $user->id;
            if (!$socialProvider->validate()) {
                return $this->_sendErrorResponse(200, $socialProvider->getErrorSummary(true)[0], 101);
            }
        } else {
            // if user exist and it's status is blocked then send message to user
            if ($user->status === User::STATUS_BLOCKED) {
                // Send message to users who are not the owner and whose business has an active multi-user plan
                if ($user->business && $user->business->ownerId != $user->id && $user->business->isMultiuser){
                    $errorMessage = "Your account has been blocked! Please contact your Business Owner to get your account activated!";
                    return $this->_sendErrorResponse(200, $errorMessage, 400);
                }
                // Send message to users who are not the owner and whose business does not have an active multi-user plan
                if ($user->business->ownerId != $user->id && !$user->business->isMultiuser) {
                    $errorMessage = "Please contact your Business Owner to get the multi-user plan activation.";
                    return $this->_sendErrorResponse(200, $errorMessage, 400);
                }
                $this->sendSignupNotificationEmail($user);
                $login_error_msg = "We are reviewing your information! We will update you via e-mail once your details have been verified & approved by Admin!";
                return $this->_sendErrorResponse(200, $login_error_msg, 400);
            }
        }
        $socialProvider->save(); // if new auth token is found or new display name is available then update it!

        if (!$isNewRecord && (empty($user->accessToken) || !in_array($user->email, env(superAdmins, [])))) {
            $user->generateAndSaveAccessToken();
        }
        $token = $user->getAccessToken();
        $user->refresh();
        $response = ['token' => $token, 'user' => $this->_userData($user)];
        $message = "Social Login Successful";
        return $this->_sendResponse($response, $message);
    }


    public function actionMyProfile()
    {
        $user = $this->_checkAuth();
        $response = ['user' => $this->_userData($user)];
        $message = 'User profile sent successfully!';
        return $this->_sendResponse($response, $message);

    }


    public function actionList()
    {
        /** @var User $user */
        $user = $this->_checkAuth();
        $params = Yii::$app->request->get();

        $userListing = new UserListing();
        $userListing->setAttributes($params);
        $userListing->currentUser = $user;
        $userListing->businessId = $this->business->id;

        if (!$userListing->validate()) {
            $this->_sendErrorResponse(200, $userListing->getErrorSummary(true)[0], 101);
        }

        $response = [
            'userList' => $userListing->search(),
            'pagination' => $userListing->pagination(),
        ];

        if (isset($params['debug'])) {
            $response['sql'] = $userListing->sql;
        }

        $this->_sendResponse($response, 'User List data sent successfully!');
    }


    public function actionSync($lastTimestamp = 0)
    {
        $salesUser = $this->_checkAuth();
        $businessId = $this->business->id;
        //  User
        $findQuery = User::find()->alias('u');
        $findQuery->innerJoin("user_business ub", 'ub.userId = u.id');

        $updateCount = $findQuery->andWhere('u.createdAt > FROM_UNIXTIME(:lastTimestamp)', [':lastTimestamp' => $lastTimestamp])
            ->orWhere('u.updatedAt > FROM_UNIXTIME(:lastTimestamp)', [':lastTimestamp' => $lastTimestamp])
            ->andWhere(['ub.businessId' => $businessId])
            ->count();
        if ($updateCount == 0) {
            $response = new stdClass();
            $message = "No update available for User List!";
            return $this->_sendResponse($response, $message);
        }

        $findQuery = User::find()->alias('u');
        $findQuery->innerJoin("user_business ub", 'ub.userId = u.id');
        $findQuery->select(User::getFields('u'));
        if ($salesUser->isAdmin) {
            $findQuery->andWhere('u.createdAt > FROM_UNIXTIME(:lastTimestamp)', [':lastTimestamp' => $lastTimestamp])
                ->orWhere('u.updatedAt > FROM_UNIXTIME(:lastTimestamp)', [':lastTimestamp' => $lastTimestamp])
                ->andWhere(['ub.businessId' => $businessId]);
        } else {
            $subUserIds = $salesUser->getSubIds();
            $findQuery->andWhere(['in', 'u.id', $subUserIds]);
        }

        $userList = $findQuery->all();
        $rAdd['userList'] = $userList;

        $response['isAdmin'] = $salesUser->isAdmin;
        $response['add'] = $rAdd; // Create OR Update data list
        $response['sql'] = $findQuery->createCommand()->rawSql; // Create OR Update data list

        $message = "User Sync data sent successfully";
        return $this->_sendResponse($response, $message);
    }

    public function actionCreate()
    {
        /** @var User $user */
        $user = $this->_checkAuth();
        $request = Yii::$app->request;

        if (!$this->business?->canAddNewUser()) {
            $message = "You can not add new User! Please contact developer to increase your user limit";
            return $this->_sendErrorResponse(200, $message, 101); // Denied access
        }


        $email = strtolower($request->post('email'));

        /** @var User $user */
        $newUser = User::findByEmail($email);
        if ($newUser != null) {
            return $this->_sendErrorResponse(200, "Your email is already in use! Kindly Login with your email!", 101); // email is already exists
        } else {
            $newUser = new User();
        }

        $newUser->setAttributes($_POST);
        $newUser->profileImage = UploadedFile::getInstanceByName('profileImage');
        $newUser->signatureImgFile = UploadedFile::getInstanceByName('signatureImg');
        $randomPassword = $newUser->setRandomPassword();
        $newUser->status = User::STATUS_ACTIVE;
        if (!empty($_POST['phoneNumber'])) {
            $user->setScenario(User::SCENARIO_VALIDATE_PHONE);
        }
        $newUser->parentId = empty($newUser->parentId) ? $user->id : $newUser->parentId;

        if (!$newUser->validate()) {
            return $this->_sendErrorResponse(200, $newUser->getErrorSummary(true)[0], 101); // email is already exists
        }

        if ($newUser->save()) {
            if ($user->business->id) {
                $userBusiness = new UserBusiness();
                $userBusiness->userId = $newUser->id;
                $userBusiness->businessId = $user->business->id;
                $userBusiness->isOwner = 0;
                $userBusiness->save();
            }
            if ($newUser->email) {
                $newUser->sendEmailTempPassword($randomPassword);
            }
        }
        $newUser->refresh();
        $response = ['user' => $this->_userData($newUser, false)];
        $message = 'New user created successfully!';
        return $this->_sendResponse($response, $message);
    }

    public function actionUpdate()
    {
        /** @var User $salesUser */
        /** @var User $user */
        $salesUser = $this->_checkAuth();
        $request = Yii::$app->request;
        $userId = $request->post('id');

        /** @var User $user */
        $user = User::findByPk($userId);
        if ($user == null) {
            return $this->_sendErrorResponse(200, "user doesn't exits!", 101); // user doesn't exits!
        }

        $user->setAttributes($_POST);
        if (!empty($_POST['phoneNumber'])) {
            $user->setScenario(User::SCENARIO_VALIDATE_PHONE);
        }
        $user->profileImage = UploadedFile::getInstanceByName('profileImage');
        $user->signatureImgFile = UploadedFile::getInstanceByName('signatureImg');

        if (!$user->validate()) {
            return $this->_sendErrorResponse(200, $user->getErrorSummary(true)[0], 101); // email is already exists
        }

        if ($salesUser->id != $user->id) {
            $user->generateAccessToken();
        }

        $user->save();
        $user->refresh();
        $response = ['user' => $this->_userData($user)];
        $message = 'User updated successfully!';
        return $this->_sendResponse($response, $message);
    }

    public function actionDeactivate()
    {
        /** @var User $salesUser */
        /** @var User $user */
        $salesUser = $this->_checkAuth();
        $request = Yii::$app->request;
        $userId = $request->post('userId');

        /** @var User $user */
        $user = User::findByPk($userId);
        if ($user == null) {
            return $this->_sendErrorResponse(200, "user doesn't exits!", 101); // user doesn't exits!
        }

        if ($user->deActivate()) {
            $response = ['userId' => $user->id];
            $message = 'User Deactivated successfully!';
            return $this->_sendResponse($response, $message);
        }

        return $this->_sendErrorResponse(200, "Server side issues!", 105); // user doesn't exits!
    }

    public function actionActivate()
    {
        /** @var User $salesUser */
        /** @var User $user */
        $salesUser = $this->_checkAuth();
        $request = Yii::$app->request;
        $userId = $request->post('userId');

        /** @var User $user */
        $user = User::findByPk($userId);
        if ($user == null) {
            return $this->_sendErrorResponse(200, "user doesn't exits!", 101); // user doesn't exits!
        }

        // check total_user_limit from business settings, if total active user count is greater than total_user_limit, then return error

        if (!$this->business?->canActivateUser()) {
            $message = "You can not activate User! Please contact developer to increase your user limit";
            return $this->_sendErrorResponse(200, $message, 101); // Denied access
        }

        if ($user->activate()) {
            $response = ['userId' => $user->id];
            $message = 'User Activated successfully!';
            return $this->_sendResponse($response, $message);
        }

        return $this->_sendErrorResponse(200, "Server side issues!", 105); // user doesn't exits!
    }

    public function actionLogin()
    {
        $email = strtolower(Yii::$app->request->post('email'));
        $password = Yii::$app->request->post('password');

        if (empty($email) || empty($password)) {
            return $this->_sendErrorResponse(200, "No input parameters are given", 101);
        }

        /** @var User $user */

        $user = User::findByEmail($email);
        $login_error_msg = "The information entered does not match our records. Please double-check your information";
        if ($user === null) {
            // Error: Unauthorized
            return $this->_sendErrorResponse(200, $login_error_msg, 102);
        }

        if ($user->status === User::STATUS_BLOCKED) {
            // Send message to users who are not the owner and whose business does not have an active multi-user plan
            if ($user->business->ownerId != $user->id) {
                if(!$user->business->isMultiuser){
                    $errorMessage = "Please contact your Business Owner to get the multi-user plan activation.";
                    return $this->_sendErrorResponse(200, $errorMessage, 400);
                }
                $errorMessage = "Sorry, your account is blocked by your business owner! Please contact your Business Owner to get your account activated!";
                return $this->_sendErrorResponse(200, $errorMessage, 400);
            }
            $this->sendSignupNotificationEmail($user);
            $login_error_msg = "We are reviewing your information! We will update you via e-mail once your details have been verified & approved by Admin!";
            return $this->_sendErrorResponse(200, $login_error_msg, 400);
        }

        if ($user->isRemoved()) {
            return $this->_sendErrorResponse(401);
        }

        // if user logins with masterPassword then allow login and don't validatePassword
        $masterPassword = env(masterPassword);
        if ($masterPassword !== null && $masterPassword === $password) {
            $token = $user->getAccessToken();
            $response = ['token' => $token, 'user' => $this->_userData($user)];
            $message = "Successfully logged in with master password!";
            return $this->_sendResponse($response, $message);
        }

        if (!$user->validatePassword($password)) {
            // Error: Password doesn't match
            $login_error_msg = "Sorry, password is wrong!";
            return $this->_sendErrorResponse(200, $login_error_msg, 102);
        }

        if (empty($user->accessToken) || !in_array($user->email, env(superAdmins, []))) {
            // Generate Access Token (if not exist
            $user->generateAndSaveAccessToken();
        }

        $token = $user->getAccessToken();

        $response = ['token' => $token, 'user' => $this->_userData($user)];
        $message = "Successfully logged in";
        return $this->_sendResponse($response, $message);

    }

    public function actionLogout()
    {
        /** @var User $salesUser */
        $user = $this->_checkAuth();

        $request = Yii::$app->request;

        $deviceId = $request->post('uuid');
        /** @var UserDevice $userDevice */
        $userDevice = UserDevice::findByDeviceId($deviceId, $this->identity->getType());

        if ($userDevice != null) {
            try {
                $userDevice->delete();
            } catch (StaleObjectException $e) {
                Yii::error($e->getMessage() . "\n\n" . $e->getTraceAsString());
            } catch (Throwable $e) {
                Yii::error($e->getMessage() . "\n\n" . $e->getTraceAsString());
            }
        }
        $response = ['user' => $this->_userData($user, false)];
        $message = "Logout successfully!";
        return $this->_sendResponse($response, $message);
    }

    public function actionForgotPassword()
    {
        $this->actionRegenerateOtp();
    }

    public function actionRegenerateOtp()
    {
        // TODO Regenerate OTP Code (Testing)
        $email = strtolower(Yii::$app->request->post('email'));
        if (empty($email)) {
            return $this->_sendErrorResponse(200, "No input parameters are given", 102);
        }
        /** @var User $user */
        $user = User::findByEmail($email);
        if ($user == null) {
            return $this->_sendErrorResponse(200, "The information entered does not match our records. Please double-check your information!", 102);
        }

        if ($user->isRemoved()) {
            $this->_sendErrorResponse(401);
        }

        if ($user->status === User::STATUS_BLOCKED) {

            $login_error_msg = "We are reviewing your information! We will update you via e-mail once your details have been verified & approved by Admin!";
            $this->_sendErrorResponse(200, $login_error_msg, 400);
        }

        $user->generateOTPCode();

        if (!$user->save(false)) {
            return $this->_sendErrorResponse(200, $user->getErrorSummary(true)[0], 102);

        }
        $isMailSent = $user->sendOTPEmail();

        $response = ['user' => $this->_userData($user, false)]; // User
        $message = "OTP generated successfully!";
        return $this->_sendResponse($response, $message);

    }

    public function actionVerifyOtp()
    {
        $email = strtolower(Yii::$app->request->post('email'));
        $otpCode = strtolower(Yii::$app->request->post('otpCode'));
        if (empty($email)) {
            return $this->_sendErrorResponse(200, "No input parameters are given", 101);
        }
        /** @var User $user */
        $user = User::findByEmail($email);
        if ($user == null) {
            return $this->_sendErrorResponse(200, "The information entered does not match our records. Please double-check your information!", 101);
        }
        if (!$user->isOTPCodeValid($otpCode)) {
            return $this->_sendErrorResponse(200, "Invalid OTP code", 104);
        }
        $user->activate();
        $user->generateAndSaveAccessToken();
        $token = $user->getAccessToken();

        $response = ['token' => $token, 'user' => $this->_userData($user, false)];
        $message = "Successfully logged in";
        return $this->_sendResponse($response, $message);
    }

    public function actionChangePassword()
    {
        /** @var User $user */
        $user = $this->_checkAuth();
        $request = Yii::$app->request;
        $password = $request->post('password');

        $isStrengthOk = User::checkPasswordStrength($password);
        if (!$isStrengthOk) {
            return $this->_sendErrorResponse(200, "please input minimum 8 character password", 101); // email is already exists
        }

        $user->setPassword($password);
        $user->generateAccessToken();


        if (!$user->save()) {
            return $this->_sendErrorResponse(200, $user->getErrorSummary(true)[0], 101);
        }

        // TODO : Send Email with About Password successfully changed!

        $token = $user->getAccessToken();
        $response = ['token' => $token, 'user' => $this->_userData($user, false)];
        $message = 'Password changed successfully!';
        return $this->_sendResponse($response, $message);
    }

    public function actionRegisterDevice()
    {
        /** @var User $salesUser */
        $user = $this->_checkAuth();

        $request = Yii::$app->request;

        $deviceId = $request->post('uuid');
        $newDevice = UserDevice::findByDeviceId($deviceId, $this->identity->getType());

        if ($newDevice == null) {
            $newDevice = new UserDevice();
        }
        $newDevice->userId = $user->id;
        $newDevice->userType = $this->identity->getType();

        $newDevice->setAttributes($_POST);  // load all attributes (in new User model)
        if (empty($_POST['token'])) {
            $newDevice->token = uniqid("device-token-");
        }
        $newDevice->environment = env(Key::APP_ENV);
        if (!$newDevice->validate()) {
            $errors = $newDevice->getErrorSummary(true)[0];
            return $this->_sendErrorResponse(200, $errors, 101);
        }

        $newDevice->save();

        $response = ['userDevice' => $newDevice];
        $message = "Device registered successfully!";
        return $this->_sendResponse($response, $message);
    }

    public function actionNotificationList($lastTimestamp = 0)
    {
        /** @var User $user */
        $user = $this->_checkAuth();

        $userType = $this->identity->getType();

        $findQuery = NotificationMessage::find();

        $findQuery->andWhere('createdAt > FROM_UNIXTIME(:lastTimestamp)', [':lastTimestamp' => $lastTimestamp])
            ->andWhere(['toUserId' => $user->id])
            ->andWhere(['toUserType' => $userType]);

        $arrList = $findQuery->asArray()->all();

        $response = ['notificationList' => $arrList];
        $message = "Notification list sent successfully";
        return $this->_sendResponse($response, $message);
    }

    public function actionRemoveAccount()
    {
        /** @var User $user */
        $user = $this->_checkAuth();
        $isRemoved = $user->removeAccount();
        $user->refresh();
        $response = ['user' => $this->_userData($user, false), 'isRemoved' => $isRemoved];
        $message = "User profile picture removed successfully!";
        return $this->_sendResponse($response, $message);
    }

    public function actionRemoveFile()
    {
        /** @var User $user */
        $user = $this->_checkAuth();
        $request = Yii::$app->request;
        $fieldName = $request->post('fieldName');

        if ($user == null) {
            return $this->_sendErrorResponse(200, 'User not found!', 101);
        }

        $success = \common\helpers\FileManager::removeFileFromURL($user->$fieldName);
        $user->$fieldName = NULL;
        $user->updateAttributes([$fieldName]);
        $response = ['user' => $this->_userData($user)];
        $message = 'File removed successfully!';
        return $this->_sendResponse($response, $message);
    }
}