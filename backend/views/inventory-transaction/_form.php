<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\InventoryTransaction */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="inventory-transaction-form">

    <?php $form = ActiveForm::begin(); ?>

    <?= $form->field($model, 'businessId')->textInput() ?>

    <?= $form->field($model, 'txnDate')->textInput() ?>

    <?= $form->field($model, 'txnType')->dropDownList([ 'inward' => 'Inward', 'outward' => 'Outward', ], ['prompt' => '']) ?>

    <?= $form->field($model, 'supplierId')->textInput() ?>

    <?= $form->field($model, 'customerId')->textInput() ?>

    <?= $form->field($model, 'notes')->textarea(['rows' => 6]) ?>

    <?= $form->field($model, 'referenceNumber')->textInput(['maxlength' => true]) ?>

    <?= $form->field($model, 'updatedById')->textInput() ?>

    <?= $form->field($model, 'isDeleted')->textInput() ?>

    <?= $form->field($model, 'createdAt')->textInput() ?>

    <?= $form->field($model, 'updatedAt')->textInput() ?>

    <?= $form->field($model, 'deletedAt')->textInput() ?>

    <div class="form-group">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
