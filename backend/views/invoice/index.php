<?php

use common\models\Business;
use common\models\Customer;
use common\models\TermsCondition;
use common\models\User;
use kartik\date\DatePicker;
use yii\grid\GridView;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use yii\widgets\Pjax;

/* @var $this yii\web\View */
/* @var $searchModel backend\models\InvoiceSearch */
/* @var $dataProvider yii\data\ActiveDataProvider */

$this->title = 'Invoices';
$this->params['breadcrumbs'][] = $this->title;
$userList = ArrayHelper::map(User::find()->select(["id", "name As fullName"])->where('1=0')->orderBy('fullName ASC')->asArray()->all(), 'id', 'fullName');
$customerList = ArrayHelper::map(Customer::find()->select(["id", "name As fullName"])->where('1=0')->orderBy('fullName ASC')->asArray()->all(), 'id', 'fullName');
$businessList = ArrayHelper::map(Business::find()->select(["id", "name"])->where('1=0')->orderBy('name ASC')->asArray()->all(), 'id', 'name');
$termList = ArrayHelper::map(TermsCondition::find()->select(["id", "text"])->where('1=0')->orderBy('text ASC')->asArray()->all(), 'id', 'name');
$date =  null;
$isAdmin = Yii::$app->session['isAdmin'];
?>
<div class="invoice-index">
    <div class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <?php $form = ActiveForm::begin(['method' => 'get']); ?>
                            <div class="row md-form">
                                <div class="col-md-4 col-lg-4 col-sm-4">
                                    <?= $form->field($model, 'DateRange')->textInput(['class' => 'form-control', 'tabindex' => '1', 'autocomplete' => "off", 'id' => 'daterange', 'value' => $date])->label('Date Range'); ?>
                                </div>
                                <div class="form-group">
                                    <?= Html::submitButton(Yii::t('app', 'Submit'), ['class' => 'btn btn-primary', 'name' => 'btnReport', 'value' => 'showreport']) ?>
                                    <?php if ($isAdmin): ?>
                                        <?= Html::submitButton(Yii::t('app', 'Download Excel'), ['class' => 'btn btn-primary', 'name' => 'btnReport', 'value' => 'excel']) ?>
                                        <?= Html::submitButton(Yii::t('app', 'Download Pdf'), ['class' => 'btn btn-primary', 'name' => 'btnReport', 'value' => 'pdf', 'formtarget' => '_blank']) ?>
                                    <?php endif; ?>
                                    <?= Html::a('Reset', ['/invoice'], ['class' => 'btn btn-primary']) ?>
                                </div>
                            </div>
                            <?php ActiveForm::end(); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header card-header-primary">
                            <h4 class="card-title text-center text-uppercase"
                                style="font-weight: 700"><?= Html::encode($this->title) ?></h4>
                            <p class="card-category"></p>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <?php Pjax::begin();
                                echo GridView::widget([
                                    'dataProvider' => $dataProvider,
                                    'filterModel' => $searchModel,
                                    'columns' => [
                                        // ['class' => 'yii\grid\SerialColumn'],

                                        [
                                            'class' => 'yii\grid\SerialColumn',
                                        ],
                                        'invoiceNumber',
                                        /* [
                                             'attribute' => 'businessId',
                                             'label'=> 'Business Name',
                                             'filter' => Html::activeDropDownList($searchModel, 'businessId', $businessList, ['class' => 'form-control', 'prompt' => '- Select -']),
                                             'value' => function ($model) {
                                                 return !empty(Yii::$app->common->getParentName('BUSINESS', $model->businessId)) ? Yii::$app->common->getParentName('BUSINESS', $model->businessId) : '-',
                                             },
                                         ],*/

                                        [
                                            'attribute' => 'assignedToId',
                                            'label' => 'User Name',
                                            //'filter' => Html::activeDropDownList($searchModel, 'assignedTo', $userList, ['class' => 'form-control', 'prompt' => '- Select -']),
                                            'value' => function ($model) {
                                                return !empty(Yii::$app->common->getParentName('USER', $model->assignedToId)) ? Yii::$app->common->getParentName('USER', $model->assignedToId) : '-';
                                            },
                                        ],
                                        [
                                            'attribute' => 'customerId',
                                            'label' => 'Customer Name',
                                            //'filter' => Html::activeDropDownList($searchModel, 'customerId', $customerList, ['class' => 'form-control', 'prompt' => '- Select -']),
                                            'value' => function ($model) {
                                                return !empty(Yii::$app->common->getParentName('CUSTOMER', $model->customerId)) ? Yii::$app->common->getParentName('CUSTOMER', $model->customerId) : '-';
                                            },
                                        ],
                                        [
                                            'attribute' => 'companyName',
                                            'label' => 'Company Name',
                                            'value' => function ($model) {
                                                return !empty(Yii::$app->common->getParentName('COMPANY', $model->customerId)) ? Yii::$app->common->getParentName('COMPANY', $model->customerId) : '-';
                                            },
                                        ],
                                        // 'termsIds',
                                        //'totalTaxAmount',
                                        [
                                            'attribute' => 'totalAmount',
                                            'label' => 'Invoice Value',
                                        ],
                                        //'totalDiscountAmount',
                                        [
                                            'attribute' => 'status',
                                            'headerOptions' => ['style' => 'width:10%'],
                                            'filter' => Html::activeDropDownList($searchModel, 'status', ["new" => "New", "revised" => "Revised", "in-progress" => "In Progress", "approved" => "Approved", "rejected" => "Rejected"], ['class' => 'form-control', 'prompt' => '- Select -']),
                                        ],
                                        // 'statusAttachment',
                                        /*[
                                            'attribute' => 'statusUpdatedById',
                                            'filter' => Html::activeDropDownList($searchModel, 'statusUpdatedById', $userList, ['class' => 'form-control', 'prompt' => '- Select -']),
                                            'value' => function ($model) {
                                                return !empty(Yii::$app->common->getParentName('USER', $model->statusUpdatedById)) ? Yii::$app->common->getParentName('USER', $model->statusUpdatedById) : '-';
                                            },
                                        ],*/
                                        //  'invoiceDate',
                                        [
                                            'attribute' => 'followupDate',
                                            'label' => 'Next Followup Date',
                                        ],
                                        //'isDeleted',
                                        //'createdAt',
                                        //'updatedAt',
                                        //'deletedAt',
                                        [
                                            'attribute' => 'invoiceDate',
                                            'label' => 'Creation Date',
                                        ],
                                        [
                                            'attribute' => 'createdAt',
                                            'value' => 'createdAt',
                                            'filter' => DatePicker::widget([
                                                'model' => $searchModel,
                                                'attribute' => 'createdAt',
                                                'pluginOptions' => [
                                                    'format' => 'yyyy-mm-dd',
                                                    'autoclose' => true,
                                                ]
                                            ]),
                                        ],
                                        [
                                            'header' => 'Action',
                                            'class' => 'yii\grid\ActionColumn',
                                            'headerOptions' => ['style' => 'width:5%'],
                                            'visible' => !Yii::$app->user->isGuest,
                                            'template' => '{print}',
                                            'buttons' => [
                                                'view' => function ($url, $model) {
                                                    $url = Yii::$app->urlManager->createUrl(['/invoice/view?id=' . base64_encode($model->id)]);
                                                    return Html::a('<span class="material-icons">visibility</span>', $url, [
                                                        'title' => Yii::t('app', 'View'),
                                                        'target' => '_blank',
                                                        'data-pjax' => '0',
                                                    ]);
                                                },
                                                'edit' => function ($url, $model) {
                                                    $url = Yii::$app->urlManager->createUrl(['/invoice/edit?invoice_number=' . base64_encode($model->id)]);
                                                    return Html::a('<span class="material-icons">edit</span>', $url, [
                                                        'title' => Yii::t('app', 'Edit Invoice'),
                                                        'target' => '_blank',
                                                        'data-pjax' => '0',
                                                    ]);
                                                },
                                                'print' => function ($url, $model) {
                                                    $url = !empty($model->pdfFileUrl) ? $model->pdfFileUrl : '#';
                                                    return Html::a('<span class="material-icons">picture_as_pdf</span>', $url, [
                                                        'title' => Yii::t('app', 'Print PDF'),
                                                        'target' => '_blank',
                                                        'data-pjax' => '0',
                                                    ]);
                                                },
                                            ],
                                        ],
                                    ],
                                    'pager' => [
                                        'prevPageLabel' => '<i class="material-icons">chevron_left</i>',
                                        'nextPageLabel' => '<i class="material-icons">chevron_right</i>',
                                        // Customzing options for pager container tag
                                        'options' => [
                                            'tag' => 'ul',
                                            'class' => 'pagination',
                                            'id' => 'pager-container',
                                        ],
                                        // Customzing CSS class for pager link
                                        'linkOptions' => ['class' => 'waves-effect'],
                                        'activePageCssClass' => 'active',
                                        'disabledPageCssClass' => 'disabled',
                                        // Customzing CSS class for navigating link
                                        'prevPageCssClass' => 'mypre',
                                        'nextPageCssClass' => 'mynext',
                                    ],
                                ]);
                                Pjax::end(); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/jquery/latest/jquery.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css"/>
<script type="text/javascript">
    $("#daterange").daterangepicker({
        maxDate: new Date(),
        locale: {
            format: 'Y-MM-DD'
        }
    });
</script>