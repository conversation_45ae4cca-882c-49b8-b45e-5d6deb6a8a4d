<?php

namespace common\services;

use common\jobs\CallableJob;
use Yii;
use yii\base\Component;
use yii\base\InvalidConfigException;
use yii\queue\JobInterface;
use yii\queue\Queue;

/**
 * QueueService provides a Laravel-like interface for working with queues in Yii2.
 *
 * This service wraps the Yii2 Queue component and provides a more fluent interface
 * similar to Laravel's Queue facade.
 *
 * @property-read Queue $queue The queue component instance
 */
class QueueService extends Component
{
    /**
     * @var string The name of the queue component to use
     */
    public $queueComponent = 'queue';

    /**
     * @var string The default queue to use
     */
    public $defaultQueue = 'default';

    /**
     * @var int The default delay in seconds
     */
    public $defaultDelay = 0;

    /**
     * @var Queue|null The queue component instance
     */
    private $_queue = null;

    /**
     * @var string The current queue name
     */
    private $_currentQueue = null;

    /**
     * @var int The current delay in seconds
     */
    private $_currentDelay = null;

    /**
     * @var bool Whether the queue is available
     */
    private $_isQueueAvailable = null;

    /**
     * @var array Options for the queue
     */
    private $_options = [];

    /**
     * @var self The singleton instance
     */
    private static $_instance = null;

    /**
     * Gets the singleton instance of QueueService
     *
     * @return self The singleton instance
     */
    public static function getInstance(): self
    {
        if (self::$_instance === null) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * Checks if the queue is available
     *
     * @return bool Whether the queue is available
     */
    public function isQueueAvailable(): bool
    {
        if (env(isQueueManagementEnabled, false)) {
            $this->_isQueueAvailable = false;
            return $this->_isQueueAvailable;
        }
        if ($this->_isQueueAvailable === null) {
            try {
                $queue = $this->getQueue();

                // For Redis queue, check connection with ping
                if (method_exists($queue, 'redis') && $queue->redis) {
                    try {
                        $ping = $queue->redis->ping();
                        $this->_isQueueAvailable = ($ping === 'PONG' || $ping === true);
                    } catch (\Exception $e) {
                        Yii::error('Redis ping failed: ' . $e->getMessage());
                        $this->_isQueueAvailable = false;
                    }
                } else {
                    // For other queue types, just check if we can get the queue instance
                    $this->_isQueueAvailable = ($queue instanceof Queue);
                }
            } catch (\Exception $e) {
                Yii::error('Queue availability check failed: ' . $e->getMessage());
                $this->_isQueueAvailable = false;
            }
        }

        return $this->_isQueueAvailable;
    }

    /**
     * Initializes the component
     */
    public function init()
    {
        parent::init();
        $this->_currentQueue = $this->defaultQueue;
        $this->_currentDelay = $this->defaultDelay;
        $this->_options = [];
    }

    /**
     * Gets the queue component instance
     *
     * @return Queue The queue component instance
     * @throws InvalidConfigException if the queue component is not found
     */
    public function getQueue(): Queue
    {
        if ($this->_queue === null) {
            if (!Yii::$app->has($this->queueComponent)) {
                throw new InvalidConfigException("The '{$this->queueComponent}' component is not found in the application.");
            }

            $this->_queue = Yii::$app->get($this->queueComponent);

            if (!($this->_queue instanceof Queue)) {
                throw new InvalidConfigException("The '{$this->queueComponent}' component must implement yii\\queue\\Queue.");
            }
        }

        return $this->_queue;
    }

    /**
     * Sets the queue to use
     *
     * @param string $queue The queue name
     * @return $this
     */
    public function onQueue($queue)
    {
        $this->_currentQueue = $queue;
        return $this;
    }

    /**
     * Sets the delay for the job
     *
     * @param int $seconds The delay in seconds
     * @return $this
     */
    public function delay($seconds)
    {
        $this->_currentDelay = $seconds;
        $this->_options['delay'] = $seconds;
        return $this;
    }

    /**
     * Sets the job to be executed after the given date
     *
     * @param \DateTime|int $dateTime The date/time or timestamp when the job should be executed
     * @return $this
     */
    public function later($dateTime)
    {
        if ($dateTime instanceof \DateTime) {
            $timestamp = $dateTime->getTimestamp();
        } else {
            $timestamp = $dateTime;
        }

        $delay = $timestamp - time();
        $delay = $delay > 0 ? $delay : 0;

        return $this->delay($delay);
    }

    /**
     * Pushes a job to the queue
     *
     * @param JobInterface|callable $job The job to push
     * @param array $data The data to pass to the job
     * @return string|int The job ID
     */
    public function push($job, $data = [])
    {
        if (is_callable($job)) {
            $job = new CallableJob(['callable' => $job, 'data' => $data]);
        } elseif (is_string($job) && class_exists($job)) {
            $job = new $job($data);
        }

        $options = $this->_options;

        // Reset options after pushing
        $this->_options = [];
        $this->_currentDelay = $this->defaultDelay;
        $this->_currentQueue = $this->defaultQueue;

        try {
            $queue = $this->getQueue();

            if (isset($options['delay']) && $options['delay'] > 0) {
                return $queue->delay($options['delay'])->push($job);
            }

            if ($this->_currentQueue !== $this->defaultQueue) {
                return $queue->push($job, $this->_currentQueue);
            }

            return $queue->push($job);
        } catch (\Exception $e) {
            Yii::error("Failed to push job to queue: " . $e->getMessage());

            // If the job has an executeSync method, we can execute it synchronously as a fallback
            if (method_exists($job, 'executeSync')) {
                Yii::warning('Queue component not available. Executing job synchronously.');
                $job->executeSync();
                // Even if the job fails, we consider the push operation successful
                // since we've handled the job in some way
                return true;
            }

            throw $e; // Re-throw the exception if we can't handle it
        }
    }

    /**
     * Pushes a job to the queue with a delay
     *
     * @param int $delay The delay in seconds
     * @param JobInterface|callable $job The job to push
     * @param array $data The data to pass to the job
     * @return string|int The job ID
     */
    public function laterOn($queue, $delay, $job, $data = [])
    {
        return $this->onQueue($queue)->later($delay)->push($job, $data);
    }

    /**
     * Pushes a job to a specific queue
     *
     * @param string $queue The queue name
     * @param JobInterface|callable $job The job to push
     * @param array $data The data to pass to the job
     * @return string|int The job ID
     */
    public function pushOn($queue, $job, $data = [])
    {
        return $this->onQueue($queue)->push($job, $data);
    }

    /**
     * Pushes a job to the queue with a delay
     *
     * @param int $delay The delay in seconds
     * @param JobInterface|callable $job The job to push
     * @param array $data The data to pass to the job
     * @return string|int The job ID
     */
    public function laterPush($delay, $job, $data = [])
    {
        return $this->delay($delay)->push($job, $data);
    }

    /**
     * Pushes a job to be processed asynchronously
     *
     * @param JobInterface|callable $job The job to push
     * @param array $data The data to pass to the job
     * @return string|int The job ID
     */
    public function async($job, $data = [])
    {
        return $this->push($job, $data);
    }

    /**
     * Pushes a job to be processed synchronously (immediately)
     *
     * @param JobInterface|callable $job The job to push
     * @param array $data The data to pass to the job
     * @return mixed The result of the job
     */
    public function sync($job, $data = [])
    {
        if (is_callable($job)) {
            return call_user_func($job, $data);
        } elseif (is_string($job) && class_exists($job)) {
            $job = new $job($data);
        }

        if (method_exists($job, 'executeSync')) {
            // If the job has an executeSync method, use it
            // This will handle errors internally and not throw exceptions
            return $job->executeSync();
        } elseif ($job instanceof JobInterface) {
            try {
                $queue = $this->getQueue();
                return $job->execute($queue);
            } catch (\Exception $e) {
                Yii::error("Queue component not available for sync execution: " . $e->getMessage());
                // Log the error but don't throw an exception
                // Return false to indicate failure but allow the process to continue
                return false;
            }
        }

        throw new InvalidConfigException('Invalid job type. Job must be a callable, a class name, or implement JobInterface.');
    }

    /**
     * Sets the number of times to attempt a job
     *
     * @param int $count The number of attempts
     * @return $this
     */
    public function attempts($count)
    {
        $this->_options['attempts'] = $count;
        return $this;
    }

    /**
     * Sets the timeout for the job
     *
     * @param int $seconds The timeout in seconds
     * @return $this
     */
    public function timeout($seconds)
    {
        $this->_options['ttr'] = $seconds;
        return $this;
    }

    /**
     * Sets the job to be unique
     *
     * @param bool $unique Whether the job should be unique
     * @return $this
     */
    public function unique($unique = true)
    {
        $this->_options['unique'] = $unique;
        return $this;
    }
}
