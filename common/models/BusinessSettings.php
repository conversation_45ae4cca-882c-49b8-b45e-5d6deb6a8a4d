<?php

namespace common\models;

use common\models\base\BusinessSettingsBase;
use common\models\enum\DefaultBusinessSettings;
use common\models\enum\Key;
use Yii;
use yii\base\Exception;

class BusinessSettings extends BusinessSettingsBase
{
    public static function generateNewSettings(Business $business)
    {
        $dbCommand = Yii::$app->db->createCommand();
        $table = self::tableName();
        $dataArray = DefaultBusinessSettings::generate($business);
        $dbCommand->batchInsert($table, ['businessId', 'group', 'type', 'key', 'value', 'description'], $dataArray)->execute();
    }


    /**
     * Finds Model by id
     *
     * @param string $id
     * @return static|null
     */
    public static function findByPk($id)
    {
        return static::findOne(['id' => $id]);
    }

    /**
     * Finds Model by id
     *
     * @param $group
     * @param $key
     * @return static|null
     */
    public static function findByGroupAndKey($group, $key, $businessId)
    {
        return static::findOne(['group' => $group, 'key' => $key, 'businessId' => $businessId]);
    }

    public function getValue()
    {
        return getValue($this->type, $this->value);
    }

    public static function UpdateRegion(Business $business, $regionCode)
    {
        $region = Region::findByCode($regionCode);
        if ($region == null) {
            $region = Region::findByCode(Region::DEFAULT_REGION);
        }
        $localeSetting = self::findByGroupAndKey(Key::GROUP_APP, Key::LOCALE_CODE, $business->id);
        if ($localeSetting == null) {
            $defaultSetting = $business->defaultSettings(Key::GROUP_APP)[Key::LOCALE_CODE];
            $localeSetting = new BusinessSettings();
            $localeSetting->setAttributes($defaultSetting);
            $localeSetting->businessId = $business->id;
        }
        $localeSetting->value = $region->locale;
        $localeSetting->save();
        $localeSetting->refresh();

        return [$localeSetting];
    }

}