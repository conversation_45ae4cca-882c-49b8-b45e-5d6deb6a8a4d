<?php

namespace common\models;

use common\models\enum\SubscriptionStatus;
use common\services\EmailService;
use Yii;
use yii\db\ActiveQuery;

/**
 * StripePaymentIntent model
 *
 * @property Business $business
 * @property SubscriptionPlan $plan
 * @property User $user
 */
class StripePaymentIntent extends \common\models\base\StripePaymentIntentBase
{
    /**
     * @return ActiveQuery
     */
    public function getBusiness()
    {
        return $this->hasOne(Business::className(), ['id' => 'businessId']);
    }

    /**
     * @return ActiveQuery
     */
    public function getPlan()
    {
        return $this->hasOne(SubscriptionPlan::className(), ['id' => 'planId']);
    }

    /**
     * @return ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(User::className(), ['id' => 'userId']);
    }

    /**
     * Finds Model by StripeSubscriptionId
     *
     * @param string $stripeSubscriptionId
     * @return static|null
     */
    public static function findByStripeSubscriptionId($stripeSubscriptionId)
    {
        return static::findOne(['providerSubscriptionId' => $stripeSubscriptionId]);
    }

    /**
     * Finds Model by StripeIntentSecret
     *
     * @param string $stripeIntentSecret
     * @return static|null
     */
    public static function findByStripeIntentSecret($stripeIntentSecret)
    {
        return static::findOne(['paymentIntentClientSecret' => $stripeIntentSecret]);
    }

    /**
     * Finds Model by primary key
     *
     * @param string $id
     * @return static|null
     */
    public static function findByPk($id)
    {
        return static::findOne(['id' => $id]);
    }

    /**
     * Activate Subscription
     * @return bool
     */
    public function activate()
    {
        $this->status = SubscriptionStatus::ACTIVE;
        $this->isActive = 1;
        return $this->save();
    }

    /**
     * Deactivate Subscription
     * @param string $status
     * @return bool
     */
    public function deactivate($status = SubscriptionStatus::EXPIRED)
    {
        $this->status = $status;
        $this->isActive = 0;
        return $this->save();
    }

    /**
     * Cancels Subscription
     * @param string $status
     * @return bool
     */
    public function cancel($status = SubscriptionStatus::CANCELLED)
    {
        return $this->deactivate($status);
    }

    /**
     * Sends payment intent email to admin
     * @return void
     */
    public function sendPaymentIntentEmail()
    {
        try {
            $emailService = new EmailService(true);
            $usageLevel = "L-" . $this->business->getUsageLevel();
            $todayDate = date("d.m.Y");

            $emailService->sendAdminMail(
                'notifications/payment-intent-create',
                [
                    'subscription' => $this,
                    'user' => $this->user,
                    'business' => $this->business,
                    'plan' => $this->plan
                ],
                "Payment-Intent $usageLevel"
            );
        } catch (\Exception $e) {
            Yii::error("Error sending payment intent email: " . $e->getMessage() . "\n" . $e->getTraceAsString());
        }
    }

    /**
     * Sends payment intent email notification
     * @param User $user
     * @param SubscriptionPlan $plan
     * @param array $provider
     * @return bool
     */
    public static function SendPaymentIntentEmailNotification($user, $plan, $provider)
    {
        try {
            $emailService = new EmailService(true);
            $usageLevel = "L-" . $user->business->getUsageLevel();
            $todayDate = date("d.m.Y");

            return $emailService->sendAdminMail(
                'notifications/payment-intent-create',
                [
                    'providerData' => $provider,
                    'user' => $user,
                    'business' => $user->business,
                    'plan' => $plan
                ],
                "Payment-Intent $usageLevel"
            );
        } catch (\Exception $e) {
            Yii::error("Error sending payment intent notification: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * Sends payment failed notification email
     * @param string $paymentIntentId
     * @param string $failureCode
     * @param string $failureMessage
     * @return bool
     */
    public static function sendPaymentFailedNotificationEmail($paymentIntentId, $failureCode, $failureMessage)
    {
        try {
            $subscription = self::find()
                ->where(['like', 'paymentIntentClientSecret', $paymentIntentId])
                ->one();

            if ($subscription === null) {
                Yii::info("No subscription found for payment intent: $paymentIntentId");
                return true;
            }

            $user = $subscription->user;
            $business = $subscription->user->business;

            $emailService = new EmailService(true);
            return $emailService->sendPaymentFailedMails(
                $user,
                $subscription,
                $failureCode,
                $failureMessage
            );
        } catch (\Exception $e) {
            Yii::error("Error sending payment failed notification: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return false;
        }
    }
}