<?php

namespace common\models\base;

use common\models\Product;
use common\models\Quotation;

/**
 * This is the model class for table "quotation_items".
*
    * @property integer $id
    * @property integer $quotationId
    * @property integer $order
    * @property integer $productId
    * @property integer $isSection
    * @property string $description
    * @property double $quantity
    * @property double $qty2
    * @property double $price
    * @property double $taxAmount
    * @property string $discountType
    * @property double $discountPercentage
    * @property double $discountAmount
    * @property double $amount
    * @property double $totalAmount
    * @property string $createdAt
    * @property string $updatedAt
    * @property string $additionalFields
    *
            * @property Product $product
            * @property Quotation $quotation
    */
class QuotationItemsBase extends \yii\db\ActiveRecord
{
/**
* @inheritdoc
*/
public static function tableName()
{
return 'quotation_items';
}

/**
* @inheritdoc
*/
public function rules()
{
        return [
            [['quotationId'], 'required'],
            [['quotationId', 'order', 'productId', 'isSection'], 'integer'],
            [['quantity', 'qty2', 'price', 'taxAmount', 'discountPercentage', 'discountAmount', 'amount', 'totalAmount'], 'number'],
            [['discountType', 'additionalFields'], 'string'],
            [['createdAt', 'updatedAt'], 'safe'],
            [['description'], 'string', 'max' => 2000],
            [['productId'], 'exist', 'skipOnError' => true, 'targetClass' => Product::className(), 'targetAttribute' => ['productId' => 'id']],
            [['quotationId'], 'exist', 'skipOnError' => true, 'targetClass' => Quotation::className(), 'targetAttribute' => ['quotationId' => 'id']],
        ];
}

/**
* @inheritdoc
*/
public function attributeLabels()
{
return [
    'id' => 'ID',
    'quotationId' => 'Quotation ID',
    'order' => 'Order',
    'productId' => 'Product ID',
    'isSection' => 'Is Section',
    'description' => 'Description',
    'quantity' => 'Quantity',
    'qty2' => 'Qty2',
    'price' => 'Price',
    'taxAmount' => 'Tax Amount',
    'discountType' => 'Discount Type',
    'discountPercentage' => 'Discount Percentage',
    'discountAmount' => 'Discount Amount',
    'amount' => 'Amount',
    'totalAmount' => 'Total Amount',
    'createdAt' => 'Created At',
    'updatedAt' => 'Updated At',
    'additionalFields' => 'Additional Fields',
];
}

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getProduct()
    {
    return $this->hasOne(Product::className(), ['id' => 'productId']);
    }

    /**
    * @return \yii\db\ActiveQuery
    */
    public function getQuotation()
    {
    return $this->hasOne(Quotation::className(), ['id' => 'quotationId']);
    }

    /**
     * @inheritdoc
     * @return \common\models\query\QuotationItemsQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\QuotationItemsQuery(get_called_class());
}
}