# Stripe Invoice Handling

This document describes how the system handles Stripe invoice events, particularly for subscription renewals.

## Overview

When a subscription is renewed in Stripe, several events are triggered:

1. An invoice is created (`invoice.created`)
2. The invoice is finalized (`invoice.finalized`)
3. Payment is attempted
4. If successful, an `invoice.paid` or `invoice.payment_succeeded` event is triggered
5. The subscription is updated (`customer.subscription.updated`)

## Implementation

The system now explicitly handles `invoice.paid` and `invoice.payment_succeeded` events to update subscription dates and details.

### Key Components

1. **Webhook Handler**: The `handleWebhook` method in `StripeHelper` now includes cases for `invoice.paid` and `invoice.payment_succeeded` events.

2. **Invoice Processing**: The new `processInvoicePayment` method handles invoice payment events by:
   - Verifying the invoice is paid
   - Retrieving the associated subscription
   - Updating subscription dates and payment details
   - Storing transaction information

3. **Subscription Date Updates**: Subscription dates are only updated when:
   - It's a new subscription (no existing dates)
   - The subscription is active AND the invoice is paid AND the status is not past_due

### Testing

A test controller (`TestStripeWebhookController`) has been added to simulate invoice.paid events for testing purposes.

To test:

```bash
./yii test-stripe-webhook/invoice-paid sub_12345
```

Replace `sub_12345` with an actual Stripe subscription ID.

## Important Notes

1. **Past Due Subscriptions**: For subscriptions with a `past_due` status, the system will not update the start and end dates when processing invoice payments.

2. **Subscription Updates**: The system still processes `customer.subscription.updated` events, but with improved logic to only update dates when appropriate.

3. **Transaction Details**: When an invoice is paid, the system stores detailed transaction information including:
   - Invoice ID and number
   - Billing reason
   - Invoice PDF URL
   - Actual amount paid and currency

## Troubleshooting

If subscription dates are not updating correctly:

1. Check if the subscription status is `past_due` (dates won't update in this case)
2. Verify that the invoice status is `paid`
3. Check the logs for any errors during webhook processing
4. Use the test controller to simulate an invoice.paid event for the subscription
