ALTER TABLE `business` CHANGE `quotationPrefix` `quotationPrefix` VARCHAR(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'Quote-', <PERSON>AN<PERSON> `piPrefix` `piPrefix` VARCHAR(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'PI-', <PERSON><PERSON><PERSON> `poPrefix` `poPrefix` VARCHAR(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'PO-', CHANGE `invoicePrefix` `invoicePrefix` VARCHAR(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'INV-', CHANGE `dnPrefix` `dnPrefix` VARCHAR(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'Order-', CHANGE `budgetPrefix` `budgetPrefix` VARCHAR(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'Order-';

ALTER TABLE `quotation` CHANGE `quotationNumber` `quotationNumber` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL;

ALTER TABLE `invoice` CHANGE `invoiceNumber` `invoiceNumber` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL;

ALTER TABLE `invoice` CHANGE `poNumber` `poNumber` VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL;
