INSERT INTO business_settings (`businessId`, `group`, `type`, `key`, `value`, `description`)
SELECT bs.`businessId`, bs.`group`, bs.`type`, "total_product_image_limit", bs.`value`, "total product image limit"
FROM `business_settings` bs
         INNER JOIN business_settings bs1 ON bs.businessId = bs1.businessId
WHERE bs.`key` LIKE "total_product_limit"
  AND bs1.key = "is_product_image_enabled"
  AND bs1.value = "1"
ORDER BY `bs`.`value` DESC;
